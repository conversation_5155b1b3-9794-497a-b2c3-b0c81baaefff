#!/usr/bin/env python3
"""
Test script to verify the fetch logs API endpoint
"""

import requests
import json

def test_fetch_logs_api():
    """Test the /fetch-logs endpoint"""

    url = "http://localhost:5555/fetch-logs"

    print("Testing simplified fetch logs API endpoint...")

    # Test the simplified endpoint (will likely fail due to sudo not being available on Windows)
    print("\n1. Testing simplified endpoint:")
    try:
        response = requests.post(url, timeout=10)
        print(f"Status Code: {response.status_code}")
        try:
            print(f"Response: {response.json()}")
        except:
            print(f"Response text: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    test_fetch_logs_api()
