from flask import Flask, render_template, send_from_directory, request, jsonify, redirect, url_for, session
import os
import subprocess
from datetime import datetime, timedelta
from functools import wraps
import pandas as pd
import requests

app = Flask(__name__)
app.secret_key = 'uproar321'

DISCORD_BOT_TOKEN = os.getenv('DISCORD_BOT_TOKEN')
DISCORD_GUILD_ID = os.getenv('DISCORD_GUILD_ID')
DISCORD_RAIDER_ROLE_ID = '1173303263832064050'
SUDO_PASSWORD = os.getenv('SUDO_PASSWORD')

def assign_discord_role(user_id: str) -> bool:
    if not DISCORD_BOT_TOKEN or not DISCORD_GUILD_ID:
        return False
        
    url = f"https://discord.com/api/v10/guilds/{DISCORD_GUILD_ID}/members/{user_id}/roles/{DISCORD_RAIDER_ROLE_ID}"
    headers = {
        "Authorization": f"Bot {DISCORD_BOT_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        bot_info_url = "https://discord.com/api/v10/users/@me"
        bot_response = requests.get(bot_info_url, headers=headers)
        bot_response.raise_for_status()
        
        member_url = f"https://discord.com/api/v10/guilds/{DISCORD_GUILD_ID}/members/{user_id}"
        member_response = requests.get(member_url, headers=headers)
        member_response.raise_for_status()
        
        roles_url = f"https://discord.com/api/v10/guilds/{DISCORD_GUILD_ID}/roles"
        roles_response = requests.get(roles_url, headers=headers)
        roles_response.raise_for_status()
        
        bot_roles = member_response.json().get('roles', [])
        bot_role_positions = [role['position'] for role in roles_response.json() if role['id'] in bot_roles]
        target_role = next((role for role in roles_response.json() if role['id'] == DISCORD_RAIDER_ROLE_ID), None)
        
        if not target_role or not bot_role_positions or max(bot_role_positions) <= target_role['position']:
            return False
        
        response = requests.put(url, headers=headers)
        response.raise_for_status()
        return True
        
    except requests.exceptions.RequestException:
        return False

def remove_discord_role(user_id: str) -> bool:
    if not DISCORD_BOT_TOKEN or not DISCORD_GUILD_ID:
        return False
        
    url = f"https://discord.com/api/v10/guilds/{DISCORD_GUILD_ID}/members/{user_id}/roles/{DISCORD_RAIDER_ROLE_ID}"
    headers = {
        "Authorization": f"Bot {DISCORD_BOT_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.delete(url, headers=headers)
        response.raise_for_status()
        return True
    except requests.exceptions.RequestException:
        return False

def requires_password(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('authenticated'):
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def get_last_updated(file_paths=None):
    if file_paths is None:
        file_paths = []
    
    if isinstance(file_paths, str):
        file_paths = [file_paths]
    
    try:
        latest_time = None
        for file_path in file_paths:
            if os.path.exists(file_path):
                mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                if latest_time is None or mod_time > latest_time:
                    latest_time = mod_time
        
        if latest_time is None:
            return "No files found"

        return latest_time.strftime("%Y-%m-%d %H:%M:%S")
    except Exception:
        return "Unknown"

def get_week_number(date):
    today = datetime.now()
    days_since_wednesday = (today.weekday() - 2) % 7
    last_reset = today - timedelta(days=days_since_wednesday)
    return date >= last_reset

@app.route('/')
def index():
    try:
        df = pd.read_csv('PS_version/characters.csv')

        url_map = {}
        try:
            registers_df = pd.read_csv('PS_version/csv_registers.csv')
            for _, row in registers_df.iterrows():
                url = row['url']
                if url and 'character' in url:
                    if url.strip() and url.strip().split('/')[-1]:
                        char_name = url.strip().split('/')[-1].capitalize()
                        url_map[char_name] = url.strip()
        except Exception:
            pass

        tier_map = {}
        try:
            tier_df = pd.read_csv('PS_version/csv_tier_pieces.csv')
            for _, row in tier_df.iterrows():
                tier_map[row['name']] = int(row['tier_Pieces'])
        except Exception:
            pass

        characters = []
        for _, row in df.iterrows():
            character = {
                'name': row['name'],
                'ilvl': row['ilvl'],
                'realm': row['realm'],
                'role': row['role'],
                'class_name': row['class_name'],
                'spec': row['spec'],
                'armor_type': row['armor_type'],
                'tier_token': row['tier_token'],
                'tier_pieces': tier_map.get(row['name'], 0),
                'url': url_map.get(row['name'], '#')
            }
            characters.append(character)

        # Sort characters by role: Tank -> Healer -> Melee DPS -> Ranged DPS
        role_priority = {
            'Tank': 1,
            'Healer': 2,
            'Melee DPS': 3,
            'Melee': 3,  # Handle both "Melee DPS" and "Melee" variants
            'Ranged DPS': 4,
            'Ranged': 4  # Handle both "Ranged DPS" and "Ranged" variants
        }

        characters.sort(key=lambda x: (role_priority.get(x['role'], 5), x['name']))

        return render_template('index.html', characters=characters,
                              last_updated=get_last_updated(['PS_version/characters.csv', 'PS_version/csv_registers.csv', 'PS_version/csv_tier_pieces.csv']))
    except Exception:
        return render_template('index.html', characters=[],
                              last_updated=get_last_updated(['PS_version/characters.csv', 'PS_version/csv_registers.csv', 'PS_version/csv_tier_pieces.csv']))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        if request.form.get('password') == 'uproar321':
            session['authenticated'] = True
            return redirect(url_for('raiders'))
        return render_template('login.html', error='Invalid password')
    return render_template('login.html')

@app.route('/raiders')
@requires_password
def raiders():
    try:
        df = pd.read_csv('PS_version/csv_registers.csv', dtype={'discord_id': str})
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})
        
        discord_roles = {}
        for _, row in discord_df.iterrows():
            user_id = str(row['ID'])
            is_raider = str(row['Raider']).lower() == 'true'
            discord_roles[user_id] = 'Assigned' if is_raider else 'Not assigned'
        
        armory_urls = []
        for _, row in df.iterrows():
            discord_id = str(row.get('discord_id', ''))
            role = discord_roles.get(discord_id, 'Not assigned')
            armory_urls.append((row['url'], row['discord'], row['status'], role))
        
        return render_template('raiders.html', armory_urls=armory_urls,
                              last_updated=get_last_updated(['PS_version/csv_registers.csv', 'discord_exports/csv_user_export.csv']))
    except Exception:
        return render_template('raiders.html', armory_urls=[],
                              last_updated=get_last_updated(['PS_version/csv_registers.csv', 'discord_exports/csv_user_export.csv']))

@app.route('/favicon.ico')
def favicon():
    return send_from_directory('static', 'favicon.ico')

@app.route('/add_url', methods=['POST'])
def add_url():
    data = request.get_json()
    if not data or not all(k in data for k in ['url', 'discord', 'discord_id']):
        return jsonify({'error': 'Missing required fields'}), 400

    try:
        df = pd.read_csv('PS_version/csv_registers.csv')
        
        new_row = {
            'url': data['url'],
            'discord': data['discord'],
            'discord_id': data['discord_id'],
            'status': 'pending'
        }
        
        df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
        df.to_csv('PS_version/csv_registers.csv', index=False)
        
        return jsonify({'message': 'URL added successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/remove_url', methods=['POST'])
def remove_url():
    data = request.get_json()
    if not data or 'url' not in data:
        return jsonify({'error': 'No URL provided'}), 400

    try:
        df = pd.read_csv('PS_version/csv_registers.csv', dtype={'discord_id': str})
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})
        
        mask = df['url'] == data['url']
        if not mask.any():
            return jsonify({'error': 'URL not found'}), 404
            
        discord_id = str(df.loc[mask, 'discord_id'].iloc[0])
        discord_user = discord_df[discord_df['ID'].astype(str) == discord_id]
        
        if not discord_user.empty:
            roles = discord_user['Roles'].iloc[0]
            
            if pd.notna(roles) and 'Uproar Raider' in roles:
                if remove_discord_role(discord_id):
                    role_index = discord_df[discord_df['ID'].astype(str) == discord_id].index[0]
                    
                    current_roles = discord_df.at[role_index, 'Roles']
                    if current_roles == 'Uproar Raider':
                        discord_df.at[role_index, 'Roles'] = ''
                    else:
                        discord_df.at[role_index, 'Roles'] = current_roles.replace(', Uproar Raider', '').replace('Uproar Raider, ', '')
                    
                    discord_df.at[role_index, 'Raider'] = 'False'
                    discord_df.to_csv('discord_exports/csv_user_export.csv', index=False)
                else:
                    return jsonify({'error': 'Failed to remove Discord role'}), 500
        
        df.loc[mask, 'status'] = 'inactive'
        df.to_csv('PS_version/csv_registers.csv', index=False)
        
        return jsonify({'message': 'Raider status updated to inactive'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/update_data', methods=['POST'])
def update_data():
    try:
        if not SUDO_PASSWORD:
            return jsonify({'error': 'Sudo password not configured'}), 500

        command = "docker exec powershell-container pwsh -file PS_version/Main.ps1"
        command_args = command.split()

        process = subprocess.Popen(['sudo', '-S'] + command_args,
                                 stdin=subprocess.PIPE,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE,
                                 universal_newlines=True)

        stdout, stderr = process.communicate(input=f"{SUDO_PASSWORD}\n")

        if process.returncode != 0:
            return jsonify({'error': f'Failed to update data: {stderr}'}), 500

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({
            'message': 'Data update completed successfully',
            'timestamp': timestamp,
            'output': stdout
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/approve_url', methods=['POST'])
def approve_url():
    data = request.get_json()
    url = data.get('url')

    if not url:
        return jsonify({'error': 'No URL provided'}), 400

    try:
        df = pd.read_csv('PS_version/csv_registers.csv', dtype={'discord_id': str})
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})

        mask = df['url'] == url
        if not mask.any():
            return jsonify({'error': 'URL not found'}), 404

        discord_id = str(df.loc[mask, 'discord_id'].iloc[0])
        discord_user = discord_df[discord_df['ID'].astype(str) == discord_id]

        if not discord_user.empty:
            roles = discord_user['Roles'].iloc[0]

            if pd.isna(roles) or 'Uproar Raider' not in roles:
                if assign_discord_role(discord_id):
                    role_index = discord_df[discord_df['ID'].astype(str) == discord_id].index[0]

                    current_roles = discord_df.at[role_index, 'Roles']
                    if pd.isna(current_roles):
                        discord_df.at[role_index, 'Roles'] = 'Uproar Raider'
                    else:
                        discord_df.at[role_index, 'Roles'] = f"{current_roles}, Uproar Raider"

                    discord_df.at[role_index, 'Raider'] = 'True'
                    discord_df.to_csv('discord_exports/csv_user_export.csv', index=False)
                else:
                    return jsonify({'error': 'Failed to assign Discord role'}), 500
        else:
            return jsonify({'error': f'Discord user not found: {discord_id}'}), 404

        df.loc[mask, 'status'] = 'approved'
        df.to_csv('PS_version/csv_registers.csv', index=False)

        return jsonify({'message': 'URL approved successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/reactivate_url', methods=['POST'])
def reactivate_url():
    data = request.get_json()
    if not data or 'url' not in data:
        return jsonify({'error': 'No URL provided'}), 400

    try:
        df = pd.read_csv('PS_version/csv_registers.csv', dtype={'discord_id': str})
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})

        mask = df['url'] == data['url']
        if not mask.any():
            return jsonify({'error': 'URL not found'}), 404

        discord_id = str(df.loc[mask, 'discord_id'].iloc[0])
        discord_user = discord_df[discord_df['ID'].astype(str) == discord_id]

        if not discord_user.empty:
            roles = discord_user['Roles'].iloc[0]

            if pd.isna(roles) or 'Uproar Raider' not in roles:
                if assign_discord_role(discord_id):
                    role_index = discord_df[discord_df['ID'].astype(str) == discord_id].index[0]

                    current_roles = discord_df.at[role_index, 'Roles']
                    if pd.isna(current_roles):
                        discord_df.at[role_index, 'Roles'] = 'Uproar Raider'
                    else:
                        discord_df.at[role_index, 'Roles'] = f"{current_roles}, Uproar Raider"

                    discord_df.at[role_index, 'Raider'] = 'True'
                    discord_df.to_csv('discord_exports/csv_user_export.csv', index=False)
                else:
                    return jsonify({'error': 'Failed to assign Discord role'}), 500
        else:
            return jsonify({'error': f'Discord user not found: {discord_id}'}), 404

        df.loc[mask, 'status'] = 'approved'
        df.to_csv('PS_version/csv_registers.csv', index=False)

        return jsonify({'message': 'Raider reactivated successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/update_discord_ids', methods=['POST'])
def update_discord_ids():
    try:
        registers_df = pd.read_csv('PS_version/csv_registers.csv')
        discord_df = pd.read_csv('discord_exports/csv_user_export.csv', dtype={'ID': str})

        discord_id_map = {}
        for _, row in discord_df.iterrows():
            full_username = row['Username']
            base_username = full_username.split('#')[0] if '#' in full_username else full_username
            base_username_no_dots = base_username.replace('.', '')
            discord_id_map[full_username] = str(row['ID'])
            discord_id_map[base_username] = str(row['ID'])
            discord_id_map[base_username_no_dots] = str(row['ID'])

        registers_df['discord_id'] = registers_df['discord'].map(discord_id_map)
        registers_df.to_csv('PS_version/csv_registers.csv', index=False)

        return jsonify({'message': 'Discord IDs updated successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/tier')
def tier():
    try:
        selected_date = request.args.get('date', 'current')

        if selected_date == 'current':
            file_path = 'PS_version/csv_tier_pieces.csv'
        else:
            file_path = f'PS_version/Archive/Tier/{selected_date}_csv_tier_pieces.csv'

        df = pd.read_csv(file_path)

        characters = []
        for _, row in df.iterrows():
            character = {
                'name': row['name'],
                'realm': row['realm'],
                'token': row['token'],
                'head': int(row['Head']),
                'shoulders': int(row['Shoulders']),
                'chest': int(row['Chest']),
                'hands': int(row['Hands']),
                'legs': int(row['Legs'])
            }
            characters.append(character)

        return render_template('tier.html', characters=characters,
                              last_updated=get_last_updated(file_path), selected_date=selected_date)
    except Exception:
        return render_template('tier.html', characters=[],
                              last_updated=get_last_updated('PS_version/csv_tier_pieces.csv'), selected_date='current')

@app.route('/get_historical_dates')
def get_historical_dates():
    try:
        archive_dir = 'PS_version/Archive/Tier'
        files = [f for f in os.listdir(archive_dir) if f.endswith('_csv_tier_pieces.csv')]
        dates = [f.split('_')[0] for f in files]
        dates.sort(reverse=True)
        return jsonify({'dates': dates})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/enchants')
def enchants():
    try:
        selected_date = request.args.get('date', 'current')

        if selected_date == 'current':
            file_path = 'PS_version/csv_enchants.csv'
        else:
            file_path = f'PS_version/Archive/Enchants/{selected_date}_csv_enchants.csv'

        df = pd.read_csv(file_path)

        characters = []
        for _, row in df.iterrows():
            character = {
                'name': row['name'],
                'realm': row['realm'],
                'Head': row['Head'],
                'Back': row['Back'],
                'Chest': row['Chest'],
                'wrist': row['wrist'],
                'Legs': row['Legs'],
                'feet': row['feet'],
                'finger_1': row['finger_1'],
                'finger_2': row['finger_2'],
                'Main_hand': row['Main_hand']
            }
            characters.append(character)

        return render_template('enchants.html', characters=characters,
                              last_updated=get_last_updated(file_path), selected_date=selected_date)
    except Exception:
        return render_template('enchants.html', characters=[],
                              last_updated=get_last_updated('PS_version/csv_enchants.csv'), selected_date='current')

@app.route('/get_historical_enchant_dates')
def get_historical_enchant_dates():
    try:
        archive_dir = 'PS_version/Archive/Enchants'
        if not os.path.exists(archive_dir):
            return jsonify({'dates': []})

        files = [f for f in os.listdir(archive_dir) if f.endswith('_csv_enchants.csv')]
        dates = [f.split('_')[0] for f in files]
        dates.sort(reverse=True)

        return jsonify({'dates': dates})
    except Exception:
        return jsonify({'dates': []})

@app.route('/logs')
def logs():
    try:
        csv_path = 'PS_version/csv_logs.csv'
        if not os.path.exists(csv_path):
            sample_data = pd.DataFrame({
                'Date': ['2023-10-15', '2023-10-15', '2023-10-16'],
                'Encounter': ['Gnarlroot', 'Igira the Cruel', 'Council of Dreams'],
                'Duration': ['5:23', '7:45', '8:12'],
                'Kill': ['True', 'False', 'True'],
                'Owner': ['Seeddy', 'Frosty', 'Healbot'],
                'Link': ['https://www.warcraftlogs.com/reports/sample1',
                         'https://www.warcraftlogs.com/reports/sample2',
                         'https://www.warcraftlogs.com/reports/sample3'],
                'Zone': ['Amirdrassil', 'Amirdrassil', 'Amirdrassil']
            })
            os.makedirs(os.path.dirname(csv_path), exist_ok=True)
            sample_data.to_csv(csv_path, index=False)

        df = pd.read_csv(csv_path)

        required_columns = ['Date', 'Encounter', 'Duration', 'Kill', 'Owner', 'Link', 'Zone']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            for col in missing_columns:
                df[col] = 'Unknown'

        if 'Encounter' in df.columns:
            df = df[~df['Encounter'].str.contains('\+', regex=True, na=False)]

        # Convert Date column to datetime for proper sorting
        if 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
            # Sort by date (most recent first)
            df = df.sort_values('Date', ascending=False)
            # Convert back to string for display
            df['Date'] = df['Date'].dt.strftime('%Y-%m-%d')

        zones = sorted(df['Zone'].unique()) if 'Zone' in df.columns else []

        # Set default values: kills only and hide duplicates
        selected_zone = request.args.get('zone', '')
        selected_kill = request.args.get('kill', 'true')  # Default to kills only
        show_duplicates = request.args.get('show_duplicates', 'false').lower() == 'true'

        # Apply zone filter
        if selected_zone and selected_zone != '':
            df = df[df['Zone'] == selected_zone]

        # Apply kill filter - handle both string and boolean values
        if selected_kill and selected_kill != '' and selected_kill.lower() != 'all':
            if selected_kill.lower() == 'true':
                # Show only kills
                df = df[df['Kill'].astype(str).str.lower().isin(['true', '1', 'yes'])]
            elif selected_kill.lower() == 'false':
                # Show only wipes
                df = df[df['Kill'].astype(str).str.lower().isin(['false', '0', 'no'])]
            # If selected_kill is 'all', show all results (no filter applied)

        # Apply duplicate filter - default to hiding duplicates
        if not show_duplicates:
            df = df.drop_duplicates(subset=['Encounter'], keep='first')

        logs = []
        for _, row in df.iterrows():
            # Convert kill value to boolean for template
            kill_value = str(row['Kill']).lower() in ['true', '1', 'yes']
            log = {
                'date': row['Date'],
                'encounter': row['Encounter'],
                'duration': row['Duration'],
                'kill': kill_value,
                'owner': row['Owner'],
                'link': row['Link'],
                'zone': row['Zone']
            }
            logs.append(log)

        return render_template('logs.html', logs=logs, zones=zones,
                              selected_zone=selected_zone,
                              selected_kill=selected_kill,
                              show_duplicates=show_duplicates,
                              last_updated=get_last_updated('PS_version/csv_logs.csv'))
    except Exception:
        return render_template('logs.html', logs=[], zones=[],
                              selected_zone='',
                              selected_kill='true',  # Default to kills only
                              show_duplicates=False,
                              last_updated=get_last_updated('PS_version/csv_logs.csv'))

@app.route('/fetch-logs', methods=['POST'])
def fetch_logs():
    try:
        if not SUDO_PASSWORD:
            return jsonify({'error': 'Sudo password not configured'}), 500

        command = "docker exec -w powershell-container pwsh -file PS_version/get_logs.ps1"
        command_args = command.split()

        process = subprocess.Popen(['sudo', '-S'] + command_args,
                                 stdin=subprocess.PIPE,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE,
                                 universal_newlines=True)

        stdout, stderr = process.communicate(input=f"{SUDO_PASSWORD}\n")

        if process.returncode != 0:
            return jsonify({'error': f'Failed to fetch logs: {stderr}'}), 500

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({
            'message': 'Logs fetched successfully',
            'timestamp': timestamp,
            'output': stdout
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/progress')
def progress():
    try:
        selected_week = request.args.get('week', 'current')

        characters_path = 'PS_version/characters.csv'
        dungeon_details_path = 'PS_version/all_dungeon_details.csv'

        if not os.path.exists(characters_path):
            return render_template('progress.html', characters=[], weeks=[],
                                  last_updated=get_last_updated([characters_path, dungeon_details_path]),
                                  error=f"Characters file not found")

        if not os.path.exists(dungeon_details_path):
            return render_template('progress.html', characters=[], weeks=[],
                                  last_updated=get_last_updated([characters_path, dungeon_details_path]),
                                  error=f"Dungeon details file not found")

        characters_df = pd.read_csv(characters_path)
        dungeon_details_df = pd.read_csv(dungeon_details_path)

        dungeon_details_df['Date'] = pd.to_datetime(dungeon_details_df['Date'], format='mixed', errors='coerce')
        dungeon_details_df['Week'] = dungeon_details_df['Date'].apply(get_week_number)

        all_weeks = sorted(dungeon_details_df['Week'].unique())

        filter_week = None
        if selected_week != 'current':
            try:
                filter_week = int(selected_week)
            except ValueError:
                filter_week = get_week_number(datetime.now())
        else:
            today = datetime.now()
            filter_week = get_week_number(today)

        characters_data = []
        for _, character_row in characters_df.iterrows():
            character_name = character_row['name']

            character_runs = dungeon_details_df[dungeon_details_df['Character'] == character_name]
            total_runs = len(character_runs)

            week_runs = character_runs[character_runs['Week'] == filter_week]
            week_runs_count = len(week_runs)

            if week_runs_count > 0:
                highest_key = week_runs['Key Level'].max()
            else:
                highest_key = 0

            characters_data.append({
                'name': character_name,
                'week_number': filter_week,
                'total_runs': total_runs,
                'this_week': week_runs_count,
                'highest': highest_key
            })

        return render_template('progress.html',
                              characters=characters_data,
                              weeks=all_weeks,
                              last_updated=get_last_updated([characters_path, dungeon_details_path]),
                              selected_week=selected_week)
    except Exception:
        return render_template('progress.html', characters=[], weeks=[],
                              last_updated=get_last_updated(['PS_version/characters.csv', 'PS_version/all_dungeon_details.csv']))

if __name__ == '__main__':
    app.run(debug=True, port=5555, host='*************')
