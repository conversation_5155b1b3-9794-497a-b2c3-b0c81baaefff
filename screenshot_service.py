import os
import time
import schedule
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from PIL import Image, ImageOps

# Create screenshots directory if it doesn't exist
SCREENSHOTS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'screenshots')
os.makedirs(SCREENSHOTS_DIR, exist_ok=True)

def invert_image_colors(image_path):
    """Invert the colors of an image to create black background with white text"""
    try:
        # Open the image
        image = Image.open(image_path)

        # Convert to RGB if not already
        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Invert the colors
        inverted_image = ImageOps.invert(image)

        # Save the inverted image
        inverted_image.save(image_path)
        print(f"Applied color inversion to: {image_path}")

    except Exception as e:
        print(f"Error inverting image colors: {str(e)}")

def take_screenshot(page_type='raiders'):
    try:
        # Set up Chrome options with more stability flags
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-background-timer-throttling')
        chrome_options.add_argument('--disable-backgrounding-occluded-windows')
        chrome_options.add_argument('--disable-renderer-backgrounding')
        chrome_options.add_argument('--disable-features=TranslateUI')
        chrome_options.add_argument('--disable-ipc-flooding-protection')
        chrome_options.add_argument('--memory-pressure-off')
        chrome_options.add_argument('--max_old_space_size=4096')
        
        # Add retry logic for connecting to Selenium
        max_retries = 3
        retry_delay = 5  # seconds
        
        for attempt in range(max_retries):
            try:
                # Connect to Selenium standalone container
                driver = webdriver.Remote(
                    command_executor='http://localhost:4444/wd/hub',
                    options=chrome_options
                )
                print("Connected to Selenium container successfully")
                break
            except Exception as e:
                if attempt == max_retries - 1:  # Last attempt
                    raise Exception(f"Failed to connect to Selenium after {max_retries} attempts: {str(e)}")
                print(f"Connection attempt {attempt + 1} failed, retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
        
        # Add longer wait time for page load
        driver.set_page_load_timeout(30)
        
        # Navigate to the appropriate page based on page_type
        if page_type == 'tier':
            base_url = 'http://*************:5555'
            driver.get(f'{base_url}/tier')
            wait_element = ".container-fluid"
            wait_by = By.CLASS_NAME
            filename = 'screenshot_tier.png'
            element_screenshot = True
        elif page_type == 'enchants':
            base_url = 'http://*************:5555'
            driver.get(f'{base_url}/enchants')
            wait_element = ".container-fluid"
            wait_by = By.CLASS_NAME
            filename = 'screenshot_enchants.png'
            element_screenshot = True
        elif page_type == 'raid_rules':
            driver.get('https://guildsofwow.com/uproarstormscale/post/7018/raids-rules')
            wait_element = "post-body"
            wait_by = By.ID
            filename = 'screenshot_rules.png'
            element_screenshot = True
        else:  # default to raiders
            base_url = 'http://*************:5555'
            driver.get(base_url)
            wait_element = ".container-fluid"
            wait_by = By.CLASS_NAME
            filename = 'screenshot_raiders.png'
            element_screenshot = True

        # Wait for the element to be present with increased timeout
        try:
            element = WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((wait_by, wait_element))
            )
            print(f"Found element: {wait_element}")
        except Exception as e:
            print(f"Could not find element {wait_element}, taking full page screenshot instead: {str(e)}")
            element = None
            element_screenshot = False

        # Add small delay to ensure page is fully rendered
        time.sleep(3)

        filepath = os.path.join(SCREENSHOTS_DIR, filename)

        # Take screenshot - either full page or specific element
        try:
            if element_screenshot and element:
                # Try element screenshot first
                element.screenshot(filepath)
                print(f"Element screenshot successful: {filepath}")
            else:
                # Fallback to full page screenshot
                driver.save_screenshot(filepath)
                print(f"Full page screenshot successful: {filepath}")
        except Exception as screenshot_error:
            print(f"Screenshot failed, trying alternative method: {str(screenshot_error)}")
            # Last resort - try full page screenshot
            driver.save_screenshot(filepath)
            print(f"Alternative screenshot method successful: {filepath}")

        # Apply color inversion for raid rules to get black background with white text
        if page_type == 'raid_rules':
            invert_image_colors(filepath)

        print(f"Screenshot saved: {filepath}")

    except Exception as e:
        print(f"Error taking screenshot of {page_type} page: {str(e)}")
    
    finally:
        if 'driver' in locals():
            try:
                driver.quit()
            except:
                print("Error while closing driver")

def main():
    # Schedule the screenshot tasks to run every hour
    schedule.every().hour.at(":00").do(take_screenshot, page_type='raiders')
    schedule.every().hour.at(":05").do(take_screenshot, page_type='tier')
    schedule.every().hour.at(":10").do(take_screenshot, page_type='enchants')
    schedule.every().hour.at(":15").do(take_screenshot, page_type='raid_rules')

    print("Screenshot service started. Taking screenshots every hour...")

    # Run the first screenshots immediately
    take_screenshot('raiders')
    take_screenshot('tier')
    take_screenshot('enchants')
    take_screenshot('raid_rules')
    
    # Keep the script running
    while True:
        schedule.run_pending()
        time.sleep(60)  # Check every minute for pending tasks

if __name__ == "__main__":
    main() 
